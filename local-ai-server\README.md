# Local AI Server pour Virtual Try-On

Ce serveur remplace l'API Gemini par des modèles open source locaux pour l'application fit-check.

## Fonctionnalités

- **Transformation en modèle** : Convertit une photo utilisateur en modèle de mode
- **Essayage virtuel** : Applique des vêtements sur le modèle
- **Variation de pose** : Génère différentes poses du modèle

## Modèles utilisés

- **IDM-VTON** (ECCV 2024) : Modèle principal pour l'essayage virtuel
- **Stable Diffusion** : Base pour la génération d'images

## Installation

### Prérequis

- Python 3.10+
- CUDA 11.7+ (recommandé pour GPU)
- Anaconda ou Miniconda
- Au moins 8GB de VRAM GPU (recommandé)

### Installation automatique

```bash
cd local-ai-server
python setup.py
```

### Installation manuelle

1. **Installer les dépendances Python :**
```bash
pip install -r requirements.txt
```

2. **C<PERSON>r IDM-VTON :**
```bash
mkdir models
cd models
git clone https://github.com/yisol/IDM-VTON.git
cd IDM-VTON
conda env create -f environment.yaml
```

3. **Télécharger les modèles pré-entraînés :**
   - Visitez : https://huggingface.co/yisol/IDM-VTON
   - Téléchargez les checkpoints dans `models/checkpoints/`

## Utilisation

### Démarrer le serveur

```bash
python main.py
```

Le serveur sera disponible à : http://localhost:8000

### API Endpoints

#### 1. Génération de modèle
```
POST /generate-model-image
Content-Type: multipart/form-data

file: Image file (user photo)
```

#### 2. Essayage virtuel
```
POST /generate-virtual-tryon
Content-Type: multipart/form-data

model_image: Base64 string (model image)
garment_file: Image file (garment)
```

#### 3. Variation de pose
```
POST /generate-pose-variation
Content-Type: multipart/form-data

tryon_image: Base64 string (try-on result)
pose_instruction: String (pose description)
```

#### 4. Health Check
```
GET /health
```

## Configuration

### Variables d'environnement

- `CUDA_VISIBLE_DEVICES` : GPU à utiliser (ex: "0")
- `MODEL_PATH` : Chemin vers les modèles (défaut: "./models")

### Optimisation des performances

Pour de meilleures performances :

1. **GPU recommandé** : RTX 3080+ avec 10GB+ VRAM
2. **CPU** : 16+ cores recommandés
3. **RAM** : 32GB+ recommandés
4. **Stockage** : SSD pour les modèles

## Intégration avec fit-check

Le serveur remplace directement les appels à l'API Gemini. Voir `services/localAiService.ts` pour l'intégration côté client.

## Dépannage

### Erreurs communes

1. **CUDA out of memory** : Réduire la taille des images ou utiliser CPU
2. **Modèles non trouvés** : Vérifier le chemin des checkpoints
3. **Port déjà utilisé** : Changer le port dans `main.py`

### Logs

Les logs sont disponibles dans la console. Niveau de log configurable dans `main.py`.

## Développement

### Structure du projet

```
local-ai-server/
├── main.py              # Serveur FastAPI principal
├── setup.py             # Script d'installation
├── requirements.txt     # Dépendances Python
├── models/              # Modèles et checkpoints
│   ├── IDM-VTON/       # Repository IDM-VTON
│   └── checkpoints/    # Modèles pré-entraînés
└── README.md           # Cette documentation
```

### Ajouter de nouveaux modèles

1. Ajouter les dépendances dans `requirements.txt`
2. Modifier `initialize_models()` dans `main.py`
3. Créer de nouveaux endpoints si nécessaire

## Licence

Ce projet utilise des modèles sous licence CC BY-NC-SA 4.0. Voir les licences individuelles des modèles pour plus de détails.
