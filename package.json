{"name": "fit-check", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "@google/genai": "^1.10.0", "react-image-crop": "^11.0.6", "framer-motion": "^11.2.12", "clsx": "^2.1.1", "tailwind-merge": "^2.4.0", "@tsparticles/react": "^3.0.0", "@tsparticles/engine": "^3.5.0", "@tsparticles/slim": "^3.5.0"}, "devDependencies": {"@types/node": "^22.14.0", "@vitejs/plugin-react": "^5.0.0", "typescript": "~5.8.2", "vite": "^6.2.0"}}