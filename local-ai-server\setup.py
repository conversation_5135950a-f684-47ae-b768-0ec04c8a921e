"""
Setup script for Local AI Server
Downloads and configures IDM-VTON model
"""

import os
import subprocess
import sys
from pathlib import Path

def run_command(command, cwd=None):
    """Run a shell command and handle errors"""
    try:
        result = subprocess.run(command, shell=True, check=True, cwd=cwd, 
                              capture_output=True, text=True)
        print(f"✓ {command}")
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"✗ Error running: {command}")
        print(f"Error: {e.stderr}")
        return None

def setup_idm_vton():
    """Setup IDM-VTON model"""
    print("Setting up IDM-VTON...")
    
    # Create models directory
    models_dir = Path("models")
    models_dir.mkdir(exist_ok=True)
    
    # Clone IDM-VTON repository
    idm_vton_dir = models_dir / "IDM-VTON"
    if not idm_vton_dir.exists():
        print("Cloning IDM-VTON repository...")
        run_command(
            "git clone https://github.com/yisol/IDM-VTON.git",
            cwd=models_dir
        )
    else:
        print("IDM-VTON already exists, updating...")
        run_command("git pull", cwd=idm_vton_dir)
    
    # Create conda environment for IDM-VTON
    print("Setting up IDM-VTON environment...")
    run_command(
        "conda env create -f environment.yaml",
        cwd=idm_vton_dir
    )
    
    print("IDM-VTON setup complete!")

def setup_environment():
    """Setup the main environment"""
    print("Setting up main environment...")
    
    # Install requirements
    run_command("pip install -r requirements.txt")
    
    print("Main environment setup complete!")

def download_models():
    """Download pre-trained models"""
    print("Downloading pre-trained models...")
    
    # Create checkpoints directory
    ckpt_dir = Path("models/checkpoints")
    ckpt_dir.mkdir(parents=True, exist_ok=True)
    
    print("Model download instructions:")
    print("1. Download IDM-VTON checkpoints from: https://huggingface.co/yisol/IDM-VTON")
    print("2. Place them in: models/checkpoints/")
    print("3. Follow the IDM-VTON README for additional setup")

def main():
    """Main setup function"""
    print("🚀 Setting up Local AI Server for Virtual Try-On")
    print("=" * 50)
    
    # Check if conda is available
    if run_command("conda --version") is None:
        print("❌ Conda is required but not found. Please install Anaconda or Miniconda.")
        sys.exit(1)
    
    # Setup main environment
    setup_environment()
    
    # Setup IDM-VTON
    setup_idm_vton()
    
    # Download models
    download_models()
    
    print("\n✅ Setup complete!")
    print("\nNext steps:")
    print("1. Download the model checkpoints as instructed above")
    print("2. Run: python main.py")
    print("3. The API will be available at http://localhost:8000")

if __name__ == "__main__":
    main()
